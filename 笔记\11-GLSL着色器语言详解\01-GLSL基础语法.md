# GLSL基础语法详解

## 概述
GLSL (OpenGL Shading Language) 是专门为图形渲染管线设计的着色器编程语言。它基于C语言语法，但针对并行计算和向量运算进行了优化。

## 1. 基本数据类型

### 1.1 标量类型
```glsl
// 浮点数类型
float a = 1.0;
float b = 3.14159;

// 整数类型
int count = 10;
int index = 0;

// 布尔类型
bool isVisible = true;
bool isEnabled = false;
```

### 1.2 向量类型
```glsl
// 2D向量
vec2 position = vec2(1.0, 2.0);
vec2 texCoord = vec2(0.5, 0.5);

// 3D向量
vec3 color = vec3(1.0, 0.0, 0.0);  // 红色
vec3 normal = vec3(0.0, 1.0, 0.0); // 向上的法向量

// 4D向量
vec4 vertex = vec4(1.0, 2.0, 3.0, 1.0);  // 齐次坐标
vec4 finalColor = vec4(1.0, 0.5, 0.2, 1.0); // RGBA颜色
```

### 1.3 矩阵类型
```glsl
// 2x2矩阵
mat2 rotation2D = mat2(
    cos(angle), -sin(angle),
    sin(angle),  cos(angle)
);

// 3x3矩阵
mat3 transform3D = mat3(1.0);  // 单位矩阵

// 4x4矩阵
mat4 modelMatrix = mat4(1.0);      // 模型矩阵
mat4 viewMatrix = mat4(1.0);       // 视图矩阵
mat4 projectionMatrix = mat4(1.0); // 投影矩阵
```

## 2. 向量分量访问

### 2.1 坐标分量访问
```glsl
vec4 position = vec4(1.0, 2.0, 3.0, 4.0);

// 使用 x, y, z, w
float x = position.x;  // 1.0
float y = position.y;  // 2.0
float z = position.z;  // 3.0
float w = position.w;  // 4.0

// 使用 r, g, b, a (颜色分量)
vec4 color = vec4(0.8, 0.6, 0.4, 1.0);
float red   = color.r;  // 0.8
float green = color.g;  // 0.6
float blue  = color.b;  // 0.4
float alpha = color.a;  // 1.0

// 使用 s, t, p, q (纹理坐标)
vec4 texCoord = vec4(0.5, 0.5, 0.0, 1.0);
float s = texCoord.s;  // 0.5
float t = texCoord.t;  // 0.5
```

### 2.2 向量重组 (Swizzling)
```glsl
vec4 original = vec4(1.0, 2.0, 3.0, 4.0);

// 重新排列分量
vec2 xy = original.xy;     // vec2(1.0, 2.0)
vec3 xyz = original.xyz;   // vec3(1.0, 2.0, 3.0)
vec3 zyx = original.zyx;   // vec3(3.0, 2.0, 1.0)

// 重复分量
vec3 xxx = original.xxx;   // vec3(1.0, 1.0, 1.0)
vec4 xyxy = original.xyxy; // vec4(1.0, 2.0, 1.0, 2.0)

// 混合不同命名方式
vec3 rgb = original.rgb;   // vec3(1.0, 2.0, 3.0)
vec2 st = original.st;     // vec2(1.0, 2.0)
```

## 3. 运算符

### 3.1 算术运算符
```glsl
float a = 5.0;
float b = 3.0;

float sum = a + b;        // 8.0
float diff = a - b;       // 2.0
float product = a * b;    // 15.0
float quotient = a / b;   // 1.666...

// 向量运算
vec3 v1 = vec3(1.0, 2.0, 3.0);
vec3 v2 = vec3(4.0, 5.0, 6.0);
vec3 result = v1 + v2;    // vec3(5.0, 7.0, 9.0)
```

### 3.2 比较运算符
```glsl
float x = 5.0;
float y = 3.0;

bool greater = x > y;     // true
bool less = x < y;        // false
bool equal = x == y;      // false
bool notEqual = x != y;   // true
bool greaterEqual = x >= y; // true
bool lessEqual = x <= y;  // false
```

### 3.3 逻辑运算符
```glsl
bool a = true;
bool b = false;

bool andResult = a && b;  // false
bool orResult = a || b;   // true
bool notResult = !a;      // false
```

## 4. 控制流语句

### 4.1 条件语句
```glsl
float intensity = 0.8;

if (intensity > 0.5) {
    // 高亮度处理
    gl_FragColor = vec4(1.0, 1.0, 1.0, 1.0);
} else if (intensity > 0.2) {
    // 中等亮度处理
    gl_FragColor = vec4(0.5, 0.5, 0.5, 1.0);
} else {
    // 低亮度处理
    gl_FragColor = vec4(0.1, 0.1, 0.1, 1.0);
}
```

### 4.2 循环语句
```glsl
// for循环
vec3 totalColor = vec3(0.0);
for (int i = 0; i < 3; i++) {
    totalColor += sampleColor(i);
}

// while循环
int count = 0;
while (count < maxIterations && !converged) {
    // 迭代计算
    count++;
}
```

## 5. 函数定义

### 5.1 基本函数定义
```glsl
// 计算两点距离
float distance2D(vec2 p1, vec2 p2) {
    vec2 diff = p1 - p2;
    return sqrt(diff.x * diff.x + diff.y * diff.y);
}

// 颜色混合函数
vec3 blendColors(vec3 color1, vec3 color2, float factor) {
    return mix(color1, color2, factor);
}

// 法向量归一化
vec3 normalizeVector(vec3 input) {
    float length = sqrt(dot(input, input));
    return input / length;
}
```

### 5.2 函数参数修饰符
```glsl
// in: 输入参数（默认）
void processInput(in vec3 inputColor) {
    // inputColor只能读取，不能修改
}

// out: 输出参数
void getTransformedPosition(in vec3 position, out vec4 result) {
    result = projectionMatrix * viewMatrix * vec4(position, 1.0);
}

// inout: 输入输出参数
void modifyColor(inout vec3 color) {
    color = color * 2.0;  // 可以修改传入的参数
}
```

## 6. 预处理指令

### 6.1 版本声明
```glsl
#version 300 es  // WebGL 2.0
#version 100     // WebGL 1.0
```

### 6.2 精度声明
```glsl
precision highp float;   // 高精度浮点数
precision mediump float; // 中等精度浮点数
precision lowp float;    // 低精度浮点数
```

### 6.3 宏定义
```glsl
#define PI 3.14159265359
#define MAX_LIGHTS 8
#define ENABLE_SHADOWS

#ifdef ENABLE_SHADOWS
    // 阴影相关代码
#endif
```

## 7. 限定符 (Qualifiers)

### 7.1 存储限定符
```glsl
// 顶点着色器
attribute vec3 a_position;  // 顶点属性（WebGL 1.0）
in vec3 a_position;         // 顶点属性（WebGL 2.0）

uniform mat4 u_modelMatrix; // 统一变量
varying vec2 v_texCoord;    // 变化变量（WebGL 1.0）
out vec2 v_texCoord;        // 输出变量（WebGL 2.0）

// 片段着色器
varying vec2 v_texCoord;    // 变化变量（WebGL 1.0）
in vec2 v_texCoord;         // 输入变量（WebGL 2.0）

uniform sampler2D u_texture; // 纹理采样器
```

### 7.2 参数限定符
```glsl
const float PI = 3.14159;   // 常量
```

## 8. 注释
```glsl
// 单行注释

/*
 * 多行注释
 * 可以跨越多行
 */
```

## 总结
GLSL的基础语法为着色器编程提供了强大而简洁的表达方式。理解这些基础概念是编写高效着色器程序的关键。下一节我们将深入探讨GLSL的内置函数和高级特性。
