# GLSL内置函数详解

## 概述
GLSL提供了丰富的内置函数库，这些函数经过GPU优化，能够高效地执行常见的数学运算、向量操作和纹理采样等任务。

## 1. 数学函数

### 1.1 基础数学函数
```glsl
// 三角函数
float angle = PI / 4.0;
float sinValue = sin(angle);        // 正弦值
float cosValue = cos(angle);        // 余弦值
float tanValue = tan(angle);        // 正切值

// 反三角函数
float asinValue = asin(0.5);        // 反正弦
float acosValue = acos(0.5);        // 反余弦
float atanValue = atan(1.0);        // 反正切
float atan2Value = atan(y, x);      // 两参数反正切

// 指数和对数函数
float powResult = pow(2.0, 3.0);    // 2的3次方 = 8.0
float expResult = exp(1.0);         // e的1次方 ≈ 2.718
float logResult = log(10.0);        // 自然对数
float exp2Result = exp2(3.0);       // 2的3次方 = 8.0
float log2Result = log2(8.0);       // 以2为底的对数 = 3.0

// 平方根函数
float sqrtResult = sqrt(16.0);      // 平方根 = 4.0
float invSqrtResult = inversesqrt(16.0); // 平方根倒数 = 0.25
```

### 1.2 取值函数
```glsl
float value = -3.7;

float absValue = abs(value);        // 绝对值 = 3.7
float signValue = sign(value);      // 符号 = -1.0
float floorValue = floor(value);    // 向下取整 = -4.0
float ceilValue = ceil(value);      // 向上取整 = -3.0
float fractValue = fract(value);    // 小数部分 = 0.3
float roundValue = round(value);    // 四舍五入 = -4.0 (WebGL 2.0)
float truncValue = trunc(value);    // 截断 = -3.0 (WebGL 2.0)
```

### 1.3 比较和选择函数
```glsl
float a = 5.0;
float b = 3.0;

float minValue = min(a, b);         // 最小值 = 3.0
float maxValue = max(a, b);         // 最大值 = 5.0
float clampValue = clamp(a, 0.0, 4.0); // 限制在范围内 = 4.0

// 向量版本
vec3 v1 = vec3(1.0, 5.0, 2.0);
vec3 v2 = vec3(3.0, 2.0, 4.0);
vec3 minVec = min(v1, v2);          // vec3(1.0, 2.0, 2.0)
vec3 maxVec = max(v1, v2);          // vec3(3.0, 5.0, 4.0)
```

## 2. 向量和矩阵函数

### 2.1 向量长度和距离
```glsl
vec3 vector = vec3(3.0, 4.0, 0.0);

float vectorLength = length(vector);    // 向量长度 = 5.0
float vectorDot = dot(vector, vector);  // 点积 = 25.0
vec3 normalizedVec = normalize(vector); // 归一化向量

// 两向量间的距离
vec3 point1 = vec3(0.0, 0.0, 0.0);
vec3 point2 = vec3(3.0, 4.0, 0.0);
float dist = distance(point1, point2);  // 距离 = 5.0
```

### 2.2 向量运算
```glsl
vec3 a = vec3(1.0, 0.0, 0.0);
vec3 b = vec3(0.0, 1.0, 0.0);

// 叉积（仅适用于3D向量）
vec3 crossProduct = cross(a, b);        // vec3(0.0, 0.0, 1.0)

// 反射向量
vec3 incident = vec3(1.0, -1.0, 0.0);
vec3 normal = vec3(0.0, 1.0, 0.0);
vec3 reflected = reflect(incident, normal); // 反射向量

// 折射向量
float eta = 1.0 / 1.33; // 空气到水的折射率
vec3 refracted = refract(incident, normal, eta);
```

### 2.3 面向量函数
```glsl
// 面向前的法向量
vec3 normal = vec3(0.0, 0.0, 1.0);
vec3 incident = vec3(0.0, 0.0, -1.0);
vec3 reference = vec3(0.0, 0.0, 1.0);
vec3 frontFacing = faceforward(normal, incident, reference);
```

## 3. 插值和混合函数

### 3.1 线性插值
```glsl
// 基础线性插值
float start = 0.0;
float end = 10.0;
float factor = 0.3;
float interpolated = mix(start, end, factor); // 3.0

// 向量插值
vec3 color1 = vec3(1.0, 0.0, 0.0); // 红色
vec3 color2 = vec3(0.0, 0.0, 1.0); // 蓝色
vec3 blendedColor = mix(color1, color2, 0.5); // 紫色

// 条件混合
vec3 result = mix(color1, color2, bvec3(true, false, true));
```

### 3.2 平滑插值
```glsl
float t = 0.5;
float smoothValue = smoothstep(0.0, 1.0, t); // S型曲线插值

// 边缘检测用的平滑阶跃
float edge0 = 0.4;
float edge1 = 0.6;
float value = 0.5;
float stepped = smoothstep(edge0, edge1, value);
```

### 3.3 阶跃函数
```glsl
float threshold = 0.5;
float input = 0.7;
float stepped = step(threshold, input); // input >= threshold ? 1.0 : 0.0

// 向量版本
vec3 thresholds = vec3(0.3, 0.5, 0.7);
vec3 inputs = vec3(0.2, 0.6, 0.8);
vec3 results = step(thresholds, inputs); // vec3(0.0, 1.0, 1.0)
```

## 4. 纹理采样函数

### 4.1 基础纹理采样
```glsl
// 2D纹理采样
uniform sampler2D u_texture;
vec2 texCoord = vec2(0.5, 0.5);
vec4 texColor = texture2D(u_texture, texCoord); // WebGL 1.0
vec4 texColor = texture(u_texture, texCoord);   // WebGL 2.0

// 立方体纹理采样
uniform samplerCube u_cubeTexture;
vec3 direction = vec3(1.0, 0.0, 0.0);
vec4 cubeColor = textureCube(u_cubeTexture, direction); // WebGL 1.0
vec4 cubeColor = texture(u_cubeTexture, direction);     // WebGL 2.0
```

### 4.2 带偏导数的纹理采样
```glsl
// 手动指定偏导数
vec2 texCoord = vec2(0.5, 0.5);
vec2 dFdx = vec2(0.01, 0.0);
vec2 dFdy = vec2(0.0, 0.01);
vec4 texColor = textureGrad(u_texture, texCoord, dFdx, dFdy);

// 指定LOD级别
float lod = 2.0;
vec4 texColorLod = textureLod(u_texture, texCoord, lod);
```

### 4.3 纹理尺寸查询
```glsl
// 获取纹理尺寸 (WebGL 2.0)
ivec2 texSize = textureSize(u_texture, 0); // mipmap级别0的尺寸
```

## 5. 几何函数

### 5.1 偏导数函数（仅片段着色器）
```glsl
// 计算屏幕空间偏导数
float value = gl_FragCoord.x;
float dFdx = dFdx(value);  // X方向偏导数
float dFdy = dFdy(value);  // Y方向偏导数
float width = fwidth(value); // abs(dFdx) + abs(dFdy)
```

## 6. 位运算函数（WebGL 2.0）

### 6.1 整数位运算
```glsl
int a = 5;  // 101 in binary
int b = 3;  // 011 in binary

int andResult = a & b;      // 按位与 = 1 (001)
int orResult = a | b;       // 按位或 = 7 (111)
int xorResult = a ^ b;      // 按位异或 = 6 (110)
int notResult = ~a;         // 按位非
int leftShift = a << 1;     // 左移 = 10 (1010)
int rightShift = a >> 1;    // 右移 = 2 (010)
```

## 7. 类型转换函数

### 7.1 显式类型转换
```glsl
// 基础类型转换
int intValue = 5;
float floatValue = float(intValue);     // 5.0
bool boolValue = bool(intValue);        // true (非零为true)

// 向量构造和转换
vec2 v2 = vec2(1.0, 2.0);
vec3 v3 = vec3(v2, 3.0);               // vec3(1.0, 2.0, 3.0)
vec4 v4 = vec4(v3, 4.0);               // vec4(1.0, 2.0, 3.0, 4.0)

// 矩阵构造
mat2 m2 = mat2(1.0, 0.0, 0.0, 1.0);   // 2x2单位矩阵
mat3 m3 = mat3(m2);                     // 扩展为3x3矩阵
```

## 8. 实用示例

### 8.1 颜色处理
```glsl
// 颜色空间转换
vec3 rgbToHsv(vec3 rgb) {
    float maxVal = max(max(rgb.r, rgb.g), rgb.b);
    float minVal = min(min(rgb.r, rgb.g), rgb.b);
    float delta = maxVal - minVal;
    
    float h = 0.0;
    float s = maxVal == 0.0 ? 0.0 : delta / maxVal;
    float v = maxVal;
    
    if (delta != 0.0) {
        if (maxVal == rgb.r) {
            h = mod((rgb.g - rgb.b) / delta, 6.0);
        } else if (maxVal == rgb.g) {
            h = (rgb.b - rgb.r) / delta + 2.0;
        } else {
            h = (rgb.r - rgb.g) / delta + 4.0;
        }
        h /= 6.0;
    }
    
    return vec3(h, s, v);
}
```

### 8.2 噪声和随机
```glsl
// 伪随机数生成
float random(vec2 st) {
    return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
}

// 简单噪声
float noise(vec2 st) {
    vec2 i = floor(st);
    vec2 f = fract(st);
    
    float a = random(i);
    float b = random(i + vec2(1.0, 0.0));
    float c = random(i + vec2(0.0, 1.0));
    float d = random(i + vec2(1.0, 1.0));
    
    vec2 u = f * f * (3.0 - 2.0 * f);
    
    return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
}
```

## 总结
GLSL内置函数提供了强大的数学和图形处理能力。熟练掌握这些函数是编写高效着色器的关键。在实际使用中，应该优先使用这些经过GPU优化的内置函数，而不是自己实现相同的功能。
