# GLSL着色器语言详解

## 目录概览

本文件夹包含了GLSL（OpenGL Shading Language）着色器语言的全面学习资料，从基础语法到高级特性，涵盖了WebGL开发中着色器编程的各个方面。

## 📚 学习路径

### 1. 基础入门
- **[01-GLSL基础语法.md](./01-GLSL基础语法.md)** - GLSL语言的基础语法和数据类型
- **[02-GLSL内置函数详解.md](./02-GLSL内置函数详解.md)** - 数学函数、向量操作、纹理采样等内置函数
- **[03-GLSL变量限定符详解.md](./03-GLSL变量限定符详解.md)** - attribute、uniform、varying等限定符的使用

### 2. 着色器类型
- **[04-GLSL顶点着色器详解.md](./04-GLSL顶点着色器详解.md)** - 顶点变换、属性处理、光照计算
- **[05-GLSL片段着色器详解.md](./05-GLSL片段着色器详解.md)** - 像素着色、纹理采样、光照模型

### 3. 高级应用
- **[06-GLSL高级特性与技巧.md](./06-GLSL高级特性与技巧.md)** - 高级算法、性能优化、调试技巧

## 🎯 学习目标

通过本系列笔记的学习，您将掌握：

### 基础技能
- ✅ GLSL语法和数据类型
- ✅ 变量限定符的正确使用
- ✅ 内置函数的应用
- ✅ 着色器程序的结构

### 核心概念
- ✅ 顶点着色器的作用和编写
- ✅ 片段着色器的功能和实现
- ✅ 坐标变换和矩阵运算
- ✅ 光照模型和材质系统

### 高级技术
- ✅ 纹理采样和处理技巧
- ✅ 程序化纹理生成
- ✅ 基于物理的渲染（PBR）
- ✅ 阴影映射和特效实现
- ✅ 性能优化策略

## 🔗 与其他笔记的关联

### 相关WebGL笔记
- **[07-WebGL着色器与程序](../07-WebGL着色器与程序/)** - WebGL中着色器的创建和管理
- **[04-WebGL纹理系统](../04-WebGL纹理系统/)** - 纹理在着色器中的使用
- **[06-WebGL矩阵与变换](../06-WebGL矩阵与变换/)** - 矩阵运算在着色器中的应用

### 实践应用
- **[09-实战示例](../09-实战示例/)** - 着色器的实际应用案例
- **[10-HTML演示](../10-HTML演示/)** - 可运行的着色器演示

## 📖 快速参考

### 常用数据类型
```glsl
// 标量类型
float, int, bool

// 向量类型
vec2, vec3, vec4    // 浮点向量
ivec2, ivec3, ivec4 // 整数向量
bvec2, bvec3, bvec4 // 布尔向量

// 矩阵类型
mat2, mat3, mat4

// 采样器类型
sampler2D, samplerCube
```

### 常用限定符
```glsl
// WebGL 1.0
attribute vec3 a_position;  // 顶点属性
uniform mat4 u_matrix;      // 统一变量
varying vec2 v_texCoord;    // 变化变量

// WebGL 2.0
in vec3 a_position;         // 输入
out vec2 v_texCoord;        // 输出
uniform mat4 u_matrix;      // 统一变量
```

### 常用内置函数
```glsl
// 数学函数
sin(), cos(), tan(), sqrt(), pow(), abs()

// 向量函数
length(), normalize(), dot(), cross(), reflect()

// 插值函数
mix(), smoothstep(), step()

// 纹理函数
texture2D(), textureCube() // WebGL 1.0
texture()                  // WebGL 2.0
```

## 🚀 学习建议

### 1. 循序渐进
- 先掌握基础语法和概念
- 理解着色器在渲染管线中的作用
- 逐步学习复杂的光照和特效技术

### 2. 实践为主
- 每学习一个概念都要编写相应的着色器代码
- 结合WebGL API进行实际测试
- 观察不同参数对渲染效果的影响

### 3. 参考实例
- 查看实战示例中的完整着色器程序
- 分析现有代码的实现思路
- 尝试修改和扩展示例代码

### 4. 性能意识
- 从一开始就培养性能优化的意识
- 了解GPU的工作原理和限制
- 学会使用调试工具分析性能

## 🔧 开发工具推荐

### 在线编辑器
- **Shadertoy** - 在线着色器编辑和分享平台
- **GLSL Sandbox** - 简单的GLSL在线编辑器
- **WebGL Playground** - WebGL和GLSL实验平台

### 调试工具
- **WebGL Inspector** - WebGL调试扩展
- **Spector.js** - WebGL帧分析工具
- **Chrome DevTools** - 内置的WebGL调试功能

### 开发环境
- **Visual Studio Code** + GLSL扩展
- **Sublime Text** + GLSL语法高亮
- **Atom** + language-glsl包

## 📝 学习记录

建议在学习过程中：
- 记录重要概念和技巧
- 保存有用的代码片段
- 整理常见问题和解决方案
- 建立个人的着色器库

## 🎨 创意实践

掌握基础知识后，可以尝试：
- 实现经典的着色器效果（水波、火焰、云朵等）
- 创建自己的材质和光照模型
- 开发程序化纹理生成器
- 实现后处理特效

---

**开始您的GLSL学习之旅吧！** 🚀

从基础语法开始，逐步深入到高级特性，相信您很快就能编写出令人惊艳的着色器程序。
